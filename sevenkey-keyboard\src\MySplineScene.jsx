import React, { useState } from 'react'
import Spline from '@splinetool/react-spline';

function MySplineScene() {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const onLoad = () => {
    setIsLoading(false);
    console.log('Spline scene loaded successfully!');
  };

  const onError = (error) => {
    setIsLoading(false);
    setHasError(true);
    console.error('Error loading Spline scene:', error);
  };

  return (
    <div style={{
      width: '100%',
      height: '100vh',
      position: 'relative',
      backgroundColor: '#000'
    }}>
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          fontSize: '18px',
          zIndex: 10
        }}>
          Loading Spline Scene...
        </div>
      )}

      {hasError && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'red',
          fontSize: '18px',
          textAlign: 'center',
          zIndex: 10
        }}>
          <div>Failed to load Spline scene</div>
          <div style={{ fontSize: '14px', marginTop: '10px' }}>
            Please check your internet connection
          </div>
        </div>
      )}

      <Spline
        scene="https://app.spline.design/ui/eb3fabb5-bb61-4c68-ac3c-a290a12c2a78"
        onLoad={onLoad}
        onError={onError}
        style={{
          width: '100%',
          height: '100%',
          opacity: isLoading ? 0 : 1,
          transition: 'opacity 0.5s ease-in-out'
        }}
      />
    </div>
  )
}

export default MySplineScene